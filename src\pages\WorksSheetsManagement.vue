<script setup lang="ts">
/**
 * @file WorkSheets Management page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import type { WorksheetProduct, WorkSheetProduct } from '@/lib/common/types';
import WorksheetTable from '@/components/worksheets/WorksheetTable.vue';
import { getWorksheetData } from '@/lib/api';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();

// Tab state
const currentTab = ref('hardware');

// Loading state
const loading = ref(false);

// Raw data from API
const rawWorksheetData = ref<WorkSheetProduct[]>([]);
const worksheetData = ref<WorksheetProduct[]>([]);

// Transform API data to display format
const transformApiData = (apiData: WorkSheetProduct[]): WorksheetProduct[] => {
  return apiData.map(item => ({
    id: item.id.toString(),
    category: item.isMainframe === 'Y' ? 'hardware' : 'software' as 'hardware' | 'software',
    _expanded: false,
    productName: { value: item.displayName },
    itemNumber: { value: item.itemNumber },
    requestSellingPrice: { value: 0 }, // Not provided in API, set to 0
    msrp: { value: item.msrp },
    percentOfMsrp: { value: 0 }, // Not provided in API, set to 0
    dsdQuantity: { value: 0 }, // Not provided in API, set to 0
    dealerITsQuantity: { value: 0 }, // Not provided in API, set to 0
    subProducts: [],
    // Additional fields for display
    displayName: item.displayName,
    modelName: item.modelName,
    description: item.description,
    wholesaleCost: item.wholesaleCost
  }));
};

// Computed data for each category
const hardwareData = computed(() =>
  worksheetData.value.filter(item => item.category === 'hardware')
);

const softwareData = computed(() =>
  worksheetData.value.filter(item => item.category === 'software')
);

// API call to fetch worksheet data
const fetchWorksheetData = async () => {
  loading.value = true;
  try {
    const response = await getWorksheetData();
    rawWorksheetData.value = response.data;
    worksheetData.value = transformApiData(response.data);
  } catch (error) {
    console.error('Error fetching worksheet data:', error);
  } finally {
    loading.value = false;
  }
};

// Handle table actions
const handleTableAction = ({ action, item }: { action: string; item: WorksheetProduct }) => {
  switch (action) {
    case 'add':
      console.log('Add new item:', item);
      // TODO: Implement add functionality
      break;
    case 'edit':
      console.log('Edit item:', item);
      // TODO: Implement edit functionality
      break;
    case 'delete':
      console.log('Delete item:', item);
      // TODO: Implement delete functionality
      break;
    default:
      console.log('Unknown action:', action, item);
  }
};

// Load data on component mount
onMounted(() => {
  fetchWorksheetData();
  // Stop page loader when component is mounted
  appStore.stopPageLoader();
});
</script>

<template>
  <div class="pa-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <h1>{{ t('page.worksheets_management.title') }}</h1>
      <v-btn color="primary" prepend-icon="add">
        {{ t('page.worksheets_management.button.new_worksheet') }}
      </v-btn>
    </div>

    <v-card class="mt-4">
      <v-tabs v-model="currentTab" grow class="mb-0">
        <v-tab value="hardware">
          <v-icon start>hardware</v-icon>
          {{ t('page.worksheets_management.tabs.hardware') }}
        </v-tab>
        <v-tab value="software">
          <v-icon start>apps</v-icon>
          {{ t('page.worksheets_management.tabs.software') }}
        </v-tab>
      </v-tabs>

      <v-divider></v-divider>

      <v-card-text>
        <v-window v-model="currentTab">
          <v-window-item value="hardware">
            <WorksheetTable
              :data="hardwareData"
              :loading="loading"
              category="hardware"
              @action="handleTableAction"
            />
          </v-window-item>

          <v-window-item value="software">
            <WorksheetTable
              :data="softwareData"
              :loading="loading"
              category="software"
              @action="handleTableAction"
            />
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </div>
</template>