<script setup lang="ts">
/**
 * @file WorkSheets Management page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import type { WorksheetProduct } from '@/lib/common/types';
import WorksheetTable from '@/components/worksheets/WorksheetTable.vue';
import { getWorksheetData } from '@/lib/api';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();

// Tab state
const currentTab = ref('hardware');

// Loading state
const loading = ref(false);

// Raw data from API
const worksheetData = ref<WorksheetProduct[]>([]);

// Computed data for each category
const hardwareData = computed(() =>
  worksheetData.value.filter(item => item.category === 'hardware')
);

const softwareData = computed(() =>
  worksheetData.value.filter(item => item.category === 'software')
);

// API call to fetch worksheet data
const fetchWorksheetData = async () => {
  loading.value = true;
  try {
    // TODO: Replace with actual API call
    const response = await getWorksheetData();
    worksheetData.value = response.data;
    
    // Mock data for now
    // worksheetData.value = [
    //   {
    //     id: 'HW-001',
    //     category: 'hardware',
    //     productName: { value: 'Canon EOS R5' },
    //     itemNumber: { value: 'EOS-R5-001' },
    //     requestSellingPrice: { value: 3899.99 },
    //     msrp: { value: 4299.99 },
    //     percentOfMsrp: { value: 90.7 },
    //     dsdQuantity: { value: 5 },
    //     dealerITsQuantity: { value: 2 },
    //     _expanded: false,
    //     subProducts: [
    //       {
    //         id: 'HW-001-SUB-1',
    //         productName: { value: 'RF 24-70mm f/2.8L IS USM' },
    //         itemNumber: { value: 'RF-24-70-001' },
    //         requestSellingPrice: { value: 2299.99 },
    //         msrp: { value: 2499.99 },
    //         percentOfMsrp: { value: 92.0 },
    //         dsdQuantity: { value: 3 },
    //         dealerITsQuantity: { value: 1 },
    //         _expanded: false
    //       }
    //     ]
    //   },
    //   {
    //     id: 'SW-001',
    //     category: 'software',
    //     productName: { value: 'Canon Professional Print & Layout' },
    //     itemNumber: { value: 'PPL-001' },
    //     requestSellingPrice: { value: 299.99 },
    //     msrp: { value: 349.99 },
    //     percentOfMsrp: { value: 85.7 },
    //     dsdQuantity: { value: 10 },
    //     dealerITsQuantity: { value: 5 },
    //     _expanded: false,
    //     subProducts: [
    //       {
    //         id: 'SW-001-SUB-1',
    //         productName: { value: 'Additional License Pack' },
    //         itemNumber: { value: 'PPL-LIC-001' },
    //         requestSellingPrice: { value: 99.99 },
    //         msrp: { value: 119.99 },
    //         percentOfMsrp: { value: 83.3 },
    //         dsdQuantity: { value: 5 },
    //         dealerITsQuantity: { value: 2 },
    //         _expanded: false
    //       }
    //     ]
    //   }
    // ];
  } catch (error) {
    console.error('Error fetching worksheet data:', error);
  } finally {
    loading.value = false;
  }
};

// Handle table actions
const handleTableAction = ({ action, item }: { action: string; item: WorksheetProduct }) => {
  switch (action) {
    case 'add':
      console.log('Add new item:', item);
      // TODO: Implement add functionality
      break;
    case 'edit':
      console.log('Edit item:', item);
      // TODO: Implement edit functionality
      break;
    case 'delete':
      console.log('Delete item:', item);
      // TODO: Implement delete functionality
      break;
    default:
      console.log('Unknown action:', action, item);
  }
};

// Load data on component mount
onMounted(() => {
  fetchWorksheetData();
  // Stop page loader when component is mounted
  appStore.stopPageLoader();
});
</script>

<template>
  <div class="pa-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <h1>{{ t('page.worksheets_management.title') }}</h1>
      <v-btn color="primary" prepend-icon="add">
        {{ t('page.worksheets_management.button.new_worksheet') }}
      </v-btn>
    </div>

    <v-card class="mt-4">
      <v-tabs v-model="currentTab" grow class="mb-0">
        <v-tab value="hardware">
          <v-icon start>hardware</v-icon>
          {{ t('page.worksheets_management.tabs.hardware') }}
        </v-tab>
        <v-tab value="software">
          <v-icon start>apps</v-icon>
          {{ t('page.worksheets_management.tabs.software') }}
        </v-tab>
      </v-tabs>

      <v-divider></v-divider>

      <v-card-text>
        <v-window v-model="currentTab">
          <v-window-item value="hardware">
            <WorksheetTable
              :data="hardwareData"
              :loading="loading"
              category="hardware"
              @action="handleTableAction"
            />
          </v-window-item>

          <v-window-item value="software">
            <WorksheetTable
              :data="softwareData"
              :loading="loading"
              category="software"
              @action="handleTableAction"
            />
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </div>
</template>