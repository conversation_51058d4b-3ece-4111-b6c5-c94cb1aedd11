<script setup lang="ts">
/**
 * @file Sales Requests page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import DataTable from '@/components/common/DataTable.vue';
import { useAppStore } from '@/stores/AppStore';
import actionPermissionChannel from '@/composables/auth/actionPermissionChannel';
import { Channel_Action } from '@/lib/common/types';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const router = useRouter();

// Add language support
const { t } = useI18n();

// Table data
const loading = ref(false);
const headers = ref([
    { title: t('page.sales_requests.table.header.id'), key: 'id' },
    { title: t('page.sales_requests.table.header.customer'), key: 'customer' },
    // { title: t('page.sales_requests.table.header.product'), key: 'product' },
    { title: t('page.sales_requests.table.header.status'), key: 'status' },
    { title: t('page.sales_requests.table.header.date'), key: 'date' },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref([
    { id: 'SR-001', customer: 'Customer one', product: 'Canon EOS R5', status: 'Pending at Price Desk ', date: '2024-04-01' },
    { id: 'SR-002', customer: 'Request 2', product: 'Canon EOS R6', status: 'Approved', date: '2024-04-02' },
    { id: 'SR-003', customer: 'Requst 3', product: 'Canon RF 70-200mm', status: 'Rejected', date: '2024-04-30' },
    { id: 'SR-004', customer: 'Requst 4', product: 'Canon EOS R3', status: 'Pending', date: '2024-04-04' },
]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
        // Handle view action
        // console.log('View request:', item);
        router.push({ name: 'pageServiceRequestForm', params: { id: item.id } }); // Assuming you want to pass the request id
    } else if (action === 'approve') {
        // Handle approve action
        router.push({ name: 'Quote', params: { id: item.id } });
        console.log('Approve request:', item);
    } else if (action === 'reject') {
        // Handle reject action
        router.push({ name: 'pageServiceApproval'});
        console.log('Reject request:', item);
    }
};

// Load data
onMounted(() => {
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
        <div class="d-flex justify-space-between align-center mb-4">
            <h1>{{ t('page.sales_requests.title') }}</h1>
            <v-btn v-if="actionPermissionChannel( Channel_Action.SALES_OPERATIONS )"
                color="primary" 
                prepend-icon="add" 
                :to="{ name: 'pageNewSalesRequest' }"
            >
                {{ t('page.sales_requests.button.new_request') }}
            </v-btn>
        </div>
        
        <v-card class="mt-4">
 
            
            <!-- <v-card-text> -->
                <DataTable 
                    :headers="headers" 
                    :items="items" 
                    :loading="loading"
                    @action="handleAction"
                >
                    <template v-slot:item.status="{ item }">
                        <v-chip
                            :color="item.status === 'Approved' ? 'success' : 
                                   item.status === 'Rejected' ? 'error' : 'warning'"
                            size="small"
                        >
                            {{ item.status }}
                        </v-chip>
                    </template>
                    <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    title="View"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item
                                    v-if="item.status === 'Pending'"
                                    prepend-icon="check_circle"
                                    title="Approve"
                                    @click="handleAction({ action: 'approve', item })"
                                ></v-list-item>
                                <v-list-item
                                    v-if="item.status === 'Pending'"
                                    prepend-icon="cancel"
                                    title="Reject"
                                    @click="handleAction({ action: 'reject', item })"
                                ></v-list-item>
                            </v-list>
                        </v-menu>
                    </template>
                </DataTable>
            <!-- </v-card-text> -->
        </v-card>
    </div>
</template>
