<script setup lang="ts">
/**
 * @file Common Worksheet Table component with expandable rows.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { WorksheetProduct } from '@/lib/common/types';

/**
 * ----
 * Props
 * ----
 */
interface Props {
  data: WorksheetProduct[];
  loading?: boolean;
  category: 'hardware' | 'software';
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

/**
 * ----
 * Emits
 * ----
 */
const emit = defineEmits<{
  action: [{ action: string; item: WorksheetProduct }];
}>();

/**
 * ----
 * Main
 * ----
 */
// Add language support
const { t } = useI18n();

// Table headers
const headers = ref([
  { title: t('page.worksheets_management.table.header.product_name'), key: 'productName', sortable: true },
  { title: t('page.worksheets_management.table.header.item_number'), key: 'itemNumber', sortable: true },
  { title: t('page.worksheets_management.table.header.dsd_quantity'), key: 'dsdQuantity', sortable: true },
  { title: t('page.worksheets_management.table.header.dealer_quantity'), key: 'dealerITsQuantity', sortable: true },
  { title: t('page.worksheets_management.table.header.selling_price'), key: 'requestSellingPrice', sortable: true },
  { title: t('page.worksheets_management.table.header.msrp'), key: 'msrp', sortable: true },
  { title: t('page.worksheets_management.table.header.percent_msrp'), key: 'percentOfMsrp', sortable: true },
  { title: t('common.actions'), key: 'actions', sortable: false }
]);

// Expanded rows tracking
const expandedRows = ref<Set<string>>(new Set());

// Toggle row expansion
const toggleExpansion = (item: WorksheetProduct) => {
  if (!item.id) return;
  
  if (expandedRows.value.has(item.id)) {
    expandedRows.value.delete(item.id);
  } else {
    expandedRows.value.add(item.id);
  }
};

// Check if row is expanded
const isExpanded = (item: WorksheetProduct) => {
  return item.id ? expandedRows.value.has(item.id) : false;
};

// Handle table actions
const handleAction = (action: string, item: WorksheetProduct) => {
  emit('action', { action, item });
};

// Format currency
const formatCurrency = (value: number | undefined) => {
  if (value === undefined) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format percentage
const formatPercentage = (value: string | number | undefined) => {
  if (value === undefined) return '0%';
  return `${value}%`;
};

// Get display value from field
const getDisplayValue = (field: { value: string | number } | undefined) => {
  return field?.value || '';
};
</script>

<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="data"
      :loading="loading"
      item-key="id"
      class="elevation-1"
    >


      <!-- Product Name Column with Expand Button -->
      <template v-slot:item.productName="{ item }">
        <div class="d-flex align-center">
          <v-btn
            v-if="item.subProducts && item.subProducts.length > 0"
            :icon="isExpanded(item) ? 'expand_less' : 'expand_more'"
            variant="text"
            size="small"
            @click="toggleExpansion(item)"
          ></v-btn>
          <span class="ml-2">{{ getDisplayValue(item.productName) }}</span>
        </div>
      </template>

      <!-- Item Number Column -->
      <template v-slot:item.itemNumber="{ item }">
        {{ getDisplayValue(item.itemNumber) }}
      </template>

      <!-- DSD Quantity Column -->
      <template v-slot:item.dsdQuantity="{ item }">
        {{ getDisplayValue(item.dsdQuantity) }}
      </template>

      <!-- Dealer Quantity Column -->
      <template v-slot:item.dealerITsQuantity="{ item }">
        {{ getDisplayValue(item.dealerITsQuantity) }}
      </template>

      <!-- Selling Price Column -->
      <template v-slot:item.requestSellingPrice="{ item }">
        {{ formatCurrency(item.requestSellingPrice?.value) }}
      </template>

      <!-- MSRP Column -->
      <template v-slot:item.msrp="{ item }">
        {{ formatCurrency(item.msrp?.value) }}
      </template>

      <!-- Percent of MSRP Column -->
      <template v-slot:item.percentOfMsrp="{ item }">
        {{ formatPercentage(item.percentOfMsrp?.value) }}
      </template>

      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              icon="more_vert"
              variant="text"
              size="small"
              v-bind="props"
            ></v-btn>
          </template>
          <v-list>
            <v-list-item
              prepend-icon="add"
              title="Add"
              @click="handleAction('add', item)"
            ></v-list-item>
            <v-list-item
              prepend-icon="edit"
              title="Edit"
              @click="handleAction('edit', item)"
            ></v-list-item>
            <v-list-item
              prepend-icon="delete"
              title="Delete"
              @click="handleAction('delete', item)"
            ></v-list-item>
          </v-list>
        </v-menu>
      </template>

      <!-- No Data Slot -->
      <template v-slot:no-data>
        <div class="text-center pa-4">
          <v-icon size="48" color="grey-lighten-1">{{ category === 'hardware' ? 'hardware' : 'apps' }}</v-icon>
          <div class="text-h6 mt-2">{{ t('page.worksheets_management.no_data', { category }) }}</div>
          <v-btn color="primary" class="mt-2" @click="handleAction('add', {})">
            {{ t('page.worksheets_management.button.add_first', { category }) }}
          </v-btn>
        </div>
      </template>
    </v-data-table>

    <!-- Expanded Sub-products Section -->
    <div v-for="item in data" :key="`expanded-${item.id}`">
      <v-card
        v-if="isExpanded(item) && item.subProducts && item.subProducts.length > 0"
        class="mt-4 ml-4"
        variant="outlined"
      >
        <v-card-title class="text-subtitle-1 bg-grey-lighten-4 d-flex align-center">
          <v-icon class="mr-2">subdirectory_arrow_right</v-icon>
          {{ t('page.worksheets_management.table.sub_products') }} - {{ getDisplayValue(item.productName) }}
        </v-card-title>
        <v-card-text class="pa-0">
          <v-data-table
            :headers="headers.slice(0, -1)"
            :items="item.subProducts"
            hide-default-footer
            class="elevation-0"
            density="compact"
          >
            <!-- Sub-product columns -->
            <template v-slot:item.productName="{ item: subItem }">
              <div class="ml-4">{{ getDisplayValue(subItem.productName) }}</div>
            </template>

            <template v-slot:item.itemNumber="{ item: subItem }">
              {{ getDisplayValue(subItem.itemNumber) }}
            </template>

            <template v-slot:item.dsdQuantity="{ item: subItem }">
              {{ getDisplayValue(subItem.dsdQuantity) }}
            </template>

            <template v-slot:item.dealerITsQuantity="{ item: subItem }">
              {{ getDisplayValue(subItem.dealerITsQuantity) }}
            </template>

            <template v-slot:item.requestSellingPrice="{ item: subItem }">
              {{ formatCurrency(subItem.requestSellingPrice?.value) }}
            </template>

            <template v-slot:item.msrp="{ item: subItem }">
              {{ formatCurrency(subItem.msrp?.value) }}
            </template>

            <template v-slot:item.percentOfMsrp="{ item: subItem }">
              {{ formatPercentage(subItem.percentOfMsrp?.value) }}
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>
