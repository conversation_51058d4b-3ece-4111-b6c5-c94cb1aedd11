<script setup lang="ts">
/**
 * @file Common Worksheet Table component with expandable rows.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { WorksheetProduct } from '@/lib/common/types';

/**
 * ----
 * Props
 * ----
 */
interface Props {
  data: WorksheetProduct[];
  loading?: boolean;
  category: 'hardware' | 'software';
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

/**
 * ----
 * Emits
 * ----
 */
const emit = defineEmits<{
  action: [{ action: string; item: WorksheetProduct }];
}>();

/**
 * ----
 * Main
 * ----
 */
// Add language support
const { t } = useI18n();

// Table headers - showing only specified columns
const headers = ref([
  { title: t('page.worksheets_management.table.header.item_number'), key: 'itemNumber', sortable: true },
  { title: t('page.worksheets_management.table.header.display_name'), key: 'displayName', sortable: true },
  { title: t('page.worksheets_management.table.header.model_name'), key: 'modelName', sortable: true },
  { title: t('page.worksheets_management.table.header.description'), key: 'description', sortable: true },
  { title: t('page.worksheets_management.table.header.msrp'), key: 'msrp', sortable: true },
  { title: t('page.worksheets_management.table.header.wholesale_cost'), key: 'wholesaleCost', sortable: true },
  { title: t('common.actions'), key: 'actions', sortable: false }
]);

// Expanded rows tracking
const expandedRows = ref<Set<string>>(new Set());

// Toggle row expansion
const toggleExpansion = (item: WorksheetProduct) => {
  if (!item.id) return;
  
  if (expandedRows.value.has(item.id)) {
    expandedRows.value.delete(item.id);
  } else {
    expandedRows.value.add(item.id);
  }
};

// Check if row is expanded
const isExpanded = (item: WorksheetProduct) => {
  return item.id ? expandedRows.value.has(item.id) : false;
};

// Handle table actions
const handleAction = (action: string, item: WorksheetProduct) => {
  emit('action', { action, item });
};

// Format currency
const formatCurrency = (value: number | undefined) => {
  if (value === undefined || value === null) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Get MSRP value (handles both number and object types)
const getMsrpValue = (msrp: { value: number } | number | undefined): number => {
  if (msrp === undefined || msrp === null) return 0;
  if (typeof msrp === 'number') return msrp;
  return msrp.value || 0;
};



// Get display value from field
const getDisplayValue = (field: { value: string | number } | undefined) => {
  return field?.value || '';
};
</script>

<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="data"
      :loading="loading"
      item-key="id"
      class="elevation-1"
    >


      <!-- Item Number Column with Expand Button -->
      <template v-slot:item.itemNumber="{ item }">
        <div class="d-flex align-center">
          <v-btn
            v-if="item.subProducts && item.subProducts.length > 0"
            :icon="isExpanded(item) ? 'expand_less' : 'expand_more'"
            variant="text"
            size="small"
            @click="toggleExpansion(item)"
          ></v-btn>
          <span class="ml-2">{{ getDisplayValue(item.itemNumber) }}</span>
        </div>
      </template>

      <!-- Display Name Column -->
      <template v-slot:item.displayName="{ item }">
        {{ item.displayName || getDisplayValue(item.productName) }}
      </template>

      <!-- Model Name Column -->
      <template v-slot:item.modelName="{ item }">
        {{ item.modelName || '' }}
      </template>

      <!-- Description Column -->
      <template v-slot:item.description="{ item }">
        {{ item.description || '' }}
      </template>

      <!-- MSRP Column -->
      <template v-slot:item.msrp="{ item }">
        {{ formatCurrency(getMsrpValue(item.msrp)) }}
      </template>

      <!-- Wholesale Cost Column -->
      <template v-slot:item.wholesaleCost="{ item }">
        {{ formatCurrency(item.wholesaleCost) }}
      </template>

      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              icon="more_vert"
              variant="text"
              size="small"
              v-bind="props"
            ></v-btn>
          </template>
          <v-list>
            <v-list-item
              prepend-icon="add"
              title="Add"
              @click="handleAction('add', item)"
            ></v-list-item>
            <v-list-item
              prepend-icon="edit"
              title="Edit"
              @click="handleAction('edit', item)"
            ></v-list-item>
            <v-list-item
              prepend-icon="delete"
              title="Delete"
              @click="handleAction('delete', item)"
            ></v-list-item>
          </v-list>
        </v-menu>
      </template>

      <!-- No Data Slot -->
      <template v-slot:no-data>
        <div class="text-center pa-4">
          <v-icon size="48" color="grey-lighten-1">{{ category === 'hardware' ? 'hardware' : 'apps' }}</v-icon>
          <div class="text-h6 mt-2">{{ t('page.worksheets_management.no_data', { category }) }}</div>
          <v-btn color="primary" class="mt-2" @click="handleAction('add', {})">
            {{ t('page.worksheets_management.button.add_first', { category }) }}
          </v-btn>
        </div>
      </template>
    </v-data-table>

    <!-- Expanded Sub-products Section -->
    <div v-for="item in data" :key="`expanded-${item.id}`">
      <v-card
        v-if="isExpanded(item) && item.subProducts && item.subProducts.length > 0"
        class="mt-4 ml-4"
        variant="outlined"
      >
        <v-card-title class="text-subtitle-1 bg-grey-lighten-4 d-flex align-center">
          <v-icon class="mr-2">subdirectory_arrow_right</v-icon>
          {{ t('page.worksheets_management.table.sub_products') }} - {{ item.displayName || getDisplayValue(item.productName) }}
        </v-card-title>
        <v-card-text class="pa-0">
          <v-data-table
            :headers="headers.slice(0, -1)"
            :items="item.subProducts"
            hide-default-footer
            class="elevation-0"
            density="compact"
          >
            <!-- Sub-product columns -->
            <template v-slot:item.itemNumber="{ item: subItem }">
              <div class="ml-4">{{ getDisplayValue(subItem.itemNumber) }}</div>
            </template>

            <template v-slot:item.displayName="{ item: subItem }">
              {{ subItem.displayName || getDisplayValue(subItem.productName) }}
            </template>

            <template v-slot:item.modelName="{ item: subItem }">
              {{ subItem.modelName || '' }}
            </template>

            <template v-slot:item.description="{ item: subItem }">
              {{ subItem.description || '' }}
            </template>

            <template v-slot:item.msrp="{ item: subItem }">
              {{ formatCurrency(getMsrpValue(subItem.msrp)) }}
            </template>

            <template v-slot:item.wholesaleCost="{ item: subItem }">
              {{ formatCurrency(subItem.wholesaleCost) }}
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>
