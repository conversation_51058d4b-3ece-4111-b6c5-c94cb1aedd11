 /**
 * @file Types and Interface's.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

import { type VSnackbar } from 'vuetify/components'

/**
 * Application store interface.
 *
 * @export
 * @interface AppStoreState
 */
export interface AppStoreState
{
    /**
     * Flag indicating if the application has been initialized.
     *
     * @type {boolean}
     * @memberof AppStoreState
     */
    appInit: boolean;

    /**
     * Array that holds loading messages.
     *
     * @type {LoadingMessage[]}
     * @memberof AppStoreState
     */
    loadingQueue : LoadingMessage[];

    /**
     * Global loading state of application. Begins as TRUE.
     *
     * @type {boolean}
     * @memberof AppStoreState
     */
    loading: boolean;

    /**
     * Flag indicating if page is loading.
     *
     * @type {boolean}
     * @memberof AppStoreState
     */
    pageLoader: boolean;

    /**
     * Value representing the current selected theme.
     *
     * @type {string}
     * @memberof AppStoreState
     */
    currentTheme: string;

    /**
     * Value representing the current (preferred) language.
     *
     * @type {string}
     * @memberof AppStoreState
     */
    currentLanguage: string;
}

/**
  * Snackbar store interface.
  *
  * @export
  * @interface SnackbarStoreState
  */
export interface SnackbarStoreState
{
    /**
     * Flag indicating if the snackbar should be displayed.
     *
     * @type {boolean}
     * @memberof SnackbarStoreState
     */
    showSnackbar: boolean;

    /**
     * Text to display in snackbar.
     *
     * @type {string}
     * @memberof SnackbarStoreState
     */
    text: string;

    /**
     * Optional icon to display with the snackbar.
     *
     * @type {string}
     * @memberof SnackbarStoreState
     */
    icon?: string;

    /**
     * Timeout in milliseconds.
     *
     * @type {number}
     * @memberof SnackbarStoreState
     */
    timeout: number;

    /**
     * Flag indicating if the snackbar should include a close button.
     *
     * @type {boolean}
     * @memberof SnackbarStoreState
     */
    close: boolean;

    /**
     * Location of the snackbar (top, bottom, left, right, center). You can add a space between the location to use more than one.
     *
     * @type {VSnackbar['location']}
     * @memberof SnackbarStoreState
     */
    location: VSnackbar['location'];

    /**
     * Display position of the snackbar (static, relative, fixed, absolute, and sticky).
     *
     * @type {VSnackbar['position']}
     * @memberof SnackbarStoreState
     */
    position: VSnackbar['position'];

    /**
     * Colour of the snackbar.
     *
     * @type {string}
     * @memberof SnackbarStoreState
     */
    color: string;

    /**
     * Variant of the snackbar (elevated (default - leave blank), flat, tonal, outlined, text, and plain).
     *
     * @type {VSnackbar['variant']}
     * @memberof SnackbarStoreState
     */
    variant: VSnackbar['variant'];
}

/**
 * User store interface.
 *
 * @export
 * @interface UserStoreState
 */
export interface UserStoreState
{
    /**
     * Flag indicating the user is authenticated.
     *
     * @type {boolean}
     * @memberof UserStoreState
     */
    authenticated: boolean;

    /**
     * Microsoft unique user ID.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    microsoftID: string | null;
    
    /**
     * Canon employee ID.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    employeeID: string | null;
    
    /**
     * Login username (ID) used to login to Microsoft SSO.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    userPrincipalName: string | null;
    
    /**
     * Employee's email address.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    emailAddress: string | null;
    
    /**
     * Employee's given (first) name.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    givenName: string | null;
    
    /**
     * Employee's sur (last) name.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    surName: string | null;
    
    /**
     * Employee's preferred display name.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    displayName: string | null;
    
    /**
     * Employee's job title.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    jobTitle: string | null;
    
    /**
     * Employee's department.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    department: string | null;
    
    /**
     * Canon company the employee works for.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    companyName: string | null;
    
    /**
     * Street address of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof UserStoreState
     */
    officeStreet: string | null;
    
    /**
     * City of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof UserStoreState
     */
    officeCity: string | null;
    
    /**
     * Province/  State of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof UserStoreState
     */
    officeState: string | null;
    
    /**
     * Postal code / Zip code of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof UserStoreState
     */
    officePostalCode: string | null;
    
    /**
     * Country of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof UserStoreState
     */
    officeCountry: string | null;
    
    /**
     * Mobile number (or just extension) for employee.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    phoneMobile: string | null;
    
    /**
     * Fax number for employee.
     *
     * @type {string}
     * @memberof UserStoreState
     */
    phoneFax: string | null;

    /**
     * Array of business phone numbers for the employee. Typically includes extension.
     *
     * @type {string[]}
     * @memberof UserStoreState
     */
    businessPhones: string[] | null;

    /**
     * Manager of user. This is a full MSGraphUser object.
     *
     * @type {UserStoreState}
     * @memberof UserStoreState
     */
    manager: MSGraphUser | null;

    /**
     * URL to users M365 profile photo.
     *
     * @type {(string | null)}
     * @memberof UserStoreState
     */
    photoURL: string | null;
}

/**
 * Message store interface.
 *
 * @export
 * @interface MessageStoreState
 */
export interface MessageStoreState
{
    /**
     * Array of messages to display.
     *
     * @type {AppMessage[]}
     * @memberof MessageStoreState
     */
    messageQueue: AppMessage[];

    /**
     * Flag indicating if the error message should be displayed.
     *
     * @type {boolean}
     * @memberof MessageStoreState
     */
    showMessage: boolean;

    /**
     * Flag indicating if application should be viewable.
     *
     * @type {boolean}
     * @memberof MessageStoreState
     */
    disableApp: boolean
}

/**
 * Application message interface.
 *
 * @export
 * @interface AppMessage
 */
export interface AppMessage
{
    /**
     * Type of message to display. 
     *
     * @type {MessageType}
     * @memberof AppMessage
     */
    type?: MessageType;

    /**
     * Icon to display (will override default).
     *
     * @type {string}
     * @memberof AppMessage
     */
    icon?: string;

    /**
     * Title to display. 
     *
     * @type {string}
     * @memberof AppMessage
     */
    title: string;

    /**
     * Subtitle to display.
     *
     * @type {string}
     * @memberof AppMessage
     */
    subtitle?: string;

    /**
     * Body of message to display.
     *
     * @type {string}
     * @memberof AppMessage
     */
    body: string;

    /**
     * Stack error details to display.
     *
     * @type {string}
     * @memberof AppMessage
     */
    stack?: string;

    /**
     * Flag indicating if generic 'contact IT' message should be displayed.
     *
     * @type {boolean}
     * @memberof AppMessage
     */
    showContactIT?: boolean;

    /**
     * Flag indicating if a close button should be displayed.
     *
     * @type {boolean}
     * @memberof AppMessage
     */
    btnClose?: boolean;

    /**
     * Arrange of custom buttons.
     *
     * @type {MessageButton[]}
     * @memberof AppMessage
     */
    btnCustom?: MessageButton[];

    /**
     * Flag indicating if refresh button should be displayed.
     *
     * @type {boolean}
     * @memberof AppMessage
     */
    btnRefresh?: boolean;

    /**
     * Flag indicating if application should be viewable.
     *
     * @type {boolean}
     * @memberof AppMessage
     */
    disableApp?: boolean;
}

/**
 * Application loading message interface.
 *
 * @export
 * @interface LoadingMessage
 */
export interface LoadingMessage
{
    /**
     * Unique id of the message.
     *
     * @type {string}
     * @memberof LoadingMessage
     */
    id: string;

    /**
     * Text to display on loading message.
     *
     * @type {string}
     * @memberof LoadingMessage
     */
    message?: string;
}

/**
 * Type of message to display.
 */
export enum MessageType
{
	/**
	 * A success themed message.
	 */
	SUCCESS = 1,
	/**
	 * A error themed message.
	 */
	ERROR = 2,
	/**
	 * A warning themed message.
	 */
	WARNING = 3,
	/**
	 * A information themed message.
	 */
	INFO = 4
}

/**
 * Message button interface.
 *
 * @export
 * @interface MessageButton
 */
export interface MessageButton
{
    /**
     * Text to display in the button.
     *
     * @type {string}
     * @memberof MessageButton
     */
    text: string;

    /**
     * Colour of the button
     *
     * @type {string}
     * @memberof MessageButton
     */
    colour: string;

    /**
     * Callback to execute after clicked.
     *
     * @memberof MessageButton
     */
    callback?: () => void;

    /**
     * Flag indicating if the message should be closed after the button clicked.
     *
     * @type {boolean}
     * @memberof MessageButton
     */
    close: boolean;
}

/**
 * Snackbar message interface.
 *
 * @export
 * @interface AppSnackbar
 */
export interface AppSnackbar
{
    /**
     * Text to display in snackbar.
     *
     * @type {string}
     * @memberof AppSnackbar
     */
    text: string;

    /**
     * Optional icon to display with snackbar.
     *
     * @type {string}
     * @memberof AppSnackbar
     */
    icon?: string;

    /**
     * Timeout in milliseconds.
     *
     * @type {number}
     * @memberof AppSnackbar
     */
    timeout?: number;

    /**
     * Flag indicating if the snackbar should include a close button.
     *
     * @type {boolean}
     * @memberof AppSnackbar
     */
    close?: boolean;

    /**
     * Location of the snackbar (top, bottom, left, right, center). You can add a space between the location to use more than one.
     *
     * @type {VSnackbar['location']}
     * @memberof AppSnackbar
     */
    location?: VSnackbar['location'];

    /**
     * Position of the snackbar.
     *
     * @type {VSnackbar['position']}
     * @memberof AppSnackbar
     */
    position?: VSnackbar['position'];

    /**
     * Colour of the snackbar.
     *
     * @type {string}
     * @memberof AppSnackbar
     */
    color?: string;

    /**
     * Variant of the snackbar.
     *
     * @type {VSnackbar['variant']}
     * @memberof AppSnackbar
     */
    variant?: VSnackbar['variant'];
}

/**
 * Canon authentication class interface.
 *
 * @export
 * @interface ICanonAuth
 */
export interface ICanonAuth
{
    /**
     * MSAL Browser public client.
     *
     * @type {PublicClientApplication}
     * @memberof ICanonAuth
     */
    client: import ("@azure/msal-browser").PublicClientApplication;

    /**
     * Current user session account details. Null if no session is active.
     *
     * @type {AccountInfo}
     * @memberof ICanonAuth
     */
    account: import ("@azure/msal-browser").AccountInfo | null;

    /**
     * Represents the current state of authentication.
     *
     * @type {CanonAuthState}
     * @memberof ICanonAuth
     */
    authState: CanonAuthState;

    /**
     * Initialize the authentication system. Processes login redirects and detects past sessions.
     *
     * @return {*}  {Promise <void>}
     * @memberof ICanonAuth
     */
    init () : Promise <void>;

    /**
     * Cleans up auth session details in browser and auth object.
     *
     * @private
     * @return {*}  {Promise <void>}
     * @memberof CanonAuth
     */
    cleanSession () : Promise <void>;

    /**
     * Returns true if user is logged in, otherwise false.
     *
     * @return {*}  {boolean}
     * @memberof ICanonAuth
     */
    isAuthenticated () : boolean;

    /**
     * Returns the roles of the currently logged in (cached) user.
     *
     * @return {*}  {(string[] | null)}
     * @memberof ICanonAuth
     */
    getRoles () : string[] | null;

    /**
     * Indicates if a user has all of the passed roles.
     * 
     * @param { string[] } [roles] - Array of roles to check.
     * 
     * @returns { boolean } - True indicates user has the role(s), otherwise false.
     */
    hasAllRoles ( roles : string[] ) : boolean

    /**
     * Indicates if a user has any of the passed roles.
     * 
     * @param { string[] } [roles] - Array of roles to check.
     * 
     * @returns { boolean } - True indicates user has the role(s), otherwise false.
     */
    hasAnyRoles ( roles : string[] ) : boolean

    /**
     * Performs necessary MSAL actions for the login process. First checks if logged in with SSO then redirects.
     *
     * @return {*}  {Promise <boolean>}
     * @memberof ICanonAuth
     */
    doLogin () : Promise <boolean>;

    /**
     * Performs the necessary MSAL actions for logout - Without logging the user out of SSO.
     *
     * @return {*}  {Promise <boolean>}
     * @memberof ICanonAuth
     */
    doLogout () : Promise <boolean>;

    /**
     * Performs a SSO login attempt.
     *
     * @return {*}  {Promise <boolean>}
     * @memberof ICanonAuth
     */
    doLoginSSO () : Promise <boolean>;

    /**
     * Performs a redirect login attempt.
     *
     * @return {*}  {Promise <boolean>}
     * @memberof ICanonAuth
     */
    doLoginRedirect () : Promise <boolean>;

    /**
     * Returns an ID and Access token with specified scopes. If no scopes are provided, uses default login scopes.
     *
     * @param {string[]} [scopes] Scopes to request. If none provided, will use default login scopes.
     * @param {boolean} [refresh] Forces a refresh of the token, obtaining a new expiring date/time.
     * @return {*}  {Promise <AuthenticationResult | null>}
     * @memberof ICanonAuth
     */
    getToken ( scopes? : string[], refresh? : boolean ) : Promise <import('@azure/msal-browser').AuthenticationResult | null>

    /**
     * Decodes a token.
     *
     * @param {string} token
     * @return {*}  {*}
     * @memberof ICanonAuth
     */
    decodeToken ( token : string ) : any
}

/**
 * Canon authentication guard interface.
 *
 * @export
 * @interface ICanonAuthGuard
 */
export interface ICanonAuthGuard
{
    /**
     * Installs the authentication guard for Vue Router.
     *
     * @param {Router} vueRouter
     * @memberof ICanonAuthGuard
     */
    install ( vueRouter : import('vue-router').Router, i18n :any ) : void;
}

/**
 * Represents the last state of authentication.
 */
export enum CanonAuthState
{
    /**
     * Authentication has not been initialized.
     */
    NOT_READY,

    /**
     * Authentication has been initialized.
     */
    READY,

    /**
     * Authentication has been processed and the user is not logged in.
     */
    NO_LOGIN,

    /**
     * Indicates the user has been authenticated and processed and there is an active session.
     */
    IN_SESSION,

    /**
     * This is a return from a MSAL redirect method that should never truly return as the redirect should have taken place.
     */
    REDIRECTED,

	/**
	 * User has not been provided access (assigned a role) to the application.
	 */
	NO_ACCESS,

    /**
	 * User has access but has been disabled by administrator.
	 */
	DISABLED,

    /**
	 * User session has expired and login is required again.
	 */
    SESSION_EXPIRED,

    /**
     * An unknown error happened.
     */
    UNKNOWN_ERROR,
}

/**
 * Employee user object.
 *
 * @export
 * @interface MSGraphUser
 */
export interface MSGraphUser
{
    /**
     * Microsoft unique user ID.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    microsoftID: string;
    
    /**
     * Canon employee ID.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    employeeID: string;
    
    /**
     * Login username (ID) used to login to Microsoft SSO.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    userPrincipalName: string;
    
    /**
     * Employee's email address.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    emailAddress: string;
    
    /**
     * Employee's given (first) name.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    givenName: string;
    
    /**
     * Employee's sur (last) name.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    surName: string;
    
    /**
     * Employee's preferred display name.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    displayName: string;
    
    /**
     * Employee's job title.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    jobTitle: string;
    
    /**
     * Employee's department.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    department: string;
    
    /**
     * Canon company the employee works for.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    companyName: string;
    
    /**
     * Street address of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    officeStreet: string;
    
    /**
     * City of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    officeCity: string;
    
    /**
     * Province/  State of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    officeState: string;
    
    /**
     * Postal code / Zip code of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    officePostalCode: string;
    
    /**
     * Country of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    officeCountry: string;
    
    /**
     * Mobile number (or just extension) for employee.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    phoneMobile: string;
    
    /**
     * Fax number for employee.
     *
     * @type {string}
     * @memberof MSGraphUser
     */
    phoneFax: string;

    /**
     * Array of business phone numbers for the employee. Typically includes extension.
     *
     * @type {string[]}
     * @memberof MSGraphUser
     */
    businessPhones: string[]

    /**
     * Manager of user. This is a full MSGraphUser object.
     *
     * @type {MSGraphUser}
     * @memberof MSGraphUser
     */
    manager: MSGraphUser | null
}

/**
 * Available sizes for M365 user photos.
 *
 * @export
 * @enum {number}
 */
export enum MSGraphUserPhotoSize
{
    /**
     * Request M365 user photo at 48x48 size.
     */
    SIZE_1 = '48x48',

    /**
     * Request M365 user photo at 64x64 size.
     */
    SIZE_2 = '64x64',

    /**
     * Request M365 user photo at 96x96 size.
     */
    SIZE_3 = '96x96',

    /**
     * Request M365 user photo at 120x120 size.
     */
    SIZE_4 = '120x120',

    /**
     * Request M365 user photo at 240x240 size.
     */
    SIZE_5 = '240x240',

    /**
     * Request M365 user photo at 360x360 size.
     */
    SIZE_6 = '360x360',

    /**
     * Request M365 user photo at 432x432 size.
     */
    SIZE_7 = '432x432',

    /**
     * Request M365 user photo at 504x504 size.
     */
    SIZE_8 = '504x504',

    /**
     * Request M365 user photo at 648x648 size.
     */
    SIZE_9 = '648x648',

    /**
     * Request M365 user photo at the largest size.
     */
    LARGEST = '1'
}

/**
 * Value indicating the role check method that should be used for determining if a user can access a route or not.
 *
 * @export
 * @enum {number}
 */
export enum RoleEnforcement
{
    /**
     * User may have one or more of the assigned route roles and they will be granted access.
     */
    ALL = 1,

    /**
     * User must have all of the assigned route roles and they will be granted access.
     */
    ANY = 2
}

/**
 * Employee demographics user object.
 *
 * @export
 * @interface EmpDemoUser
 */
export interface EmpDemoUser
{
    /**
     * Canon employee ID.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    employeeID: string | null;
    
    /**
     * Employee's email address.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    emailAddress: string | null;
    
    /**
     * Employee's given (first) name.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    givenName: string | null;
    
    /**
     * Employee's sur (last) name.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    surName: string | null;
    
    /**
     * Employee's preferred display name.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    displayName: string | null;
    
    /**
     * Employee's job title.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    jobTitle: string | null;
    
    /**
     * Employee's department.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    department: string | null;
    
    /**
     * Canon company the employee works for.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    companyName: string | null;
    
    /**
     * Street address of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    officeStreet: string | null;
    
    /**
     * City of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    officeCity: string | null;
    
    /**
     * Province/  State of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    officeState: string | null;
    
    /**
     * Postal code / Zip code of primary office the employee is assigned to (works from).
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    officePostalCode: string | null;
    
    /**
     * Mobile number (or just extension) for employee.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    phoneMobile: string | null;
    
    /**
     * Fax number for employee.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    phoneFax: string | null;

    /**
     * Manager name of user.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    manager: string | null;

    /**
     * Manager ID of user.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    managerID: string | null;

    /**
     * Canon company short form code.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    companyCode: string | null;

    /**
     * Additional HR definition of department.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    departmentDesc: string | null;

    /**
     * ID code for department used by HR.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    departmentID: string | null;

    /**
     * List of direct reports for employee.
     *
     * @type {(string[] | null)}
     * @memberof EmpDemoUser
     */
    directReports: string | null;

    /**
     * Employee's GL code.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    employeeGL: string | null;

    /**
     * Employee active status.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    employeeStatus: string | null;

    /**
     * Employee type.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    employeeType: string | null;

    /**
     * FLSA status of employee.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    flsaStatus: string | null;

    /**
     * Short form job code.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    jobCode: string | null;

    /**
     * Date the employee started with Canon.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    jobStartDate: string | null;

    /**
     * Number of hours employee works per week.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    jobWorkWeekHours: string | null;

    /**
     * Date the record was last updated
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    lastUpdated: string | null;

    /**
     * Employee level used by HR.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    managerLevel: string | null;

    /**
     * Office phone extension.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    officeExtension: string | null;

    /**
     * Office location used by HR.
     *
     * @type {string}
     * @memberof EmpDemoUser
     */
    officeLocation: string;

    /**
     * Office location code used by HR.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    officeLocationCode: string | null;

    /**
     * Success share ID.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    successShareID: string | null;

    /**
     * Union employee flag.
     *
     * @type {(string | null)}
     * @memberof EmpDemoUser
     */
    unionEmp: string | null;
}

/**
 * Defines an application action.
 */
export enum CN_Action
{
    /**
    * User Login: Allows users to login to principal space application.
     */
    PA_LOGIN = "PA_LOGIN",

    /**
     * User Profile Update: Allows users to update their profile preferences.
     */
    PA_USER_UPDATE = "PA_USER_UPDATE",

    /**
     * Developer: Allows access to developer tools/features/pages/etc.
     */
    TROUBLESHOOT = "TROUBLESHOOT",

    ADMIN = "ADMIN",



}

export enum Channel_Action
{
        // ------------------------------------
    // UserRole-based Actions
    // ------------------------------------

    /**
     * Sales Operations: Allows access to sales-related features
     */
    SALES_OPERATIONS = "SALES_OPERATIONS",

      /**
     * Sales Management: Allows access to sales management features
     */
    SALES_MANAGER_REGIONAL_LEADERSHIP = "SALES_MANAGER_REGIONAL_LEADERSHIP",

    /**
     * Sales Management: Allows access to sales management features
     */
    SALES_MANAGEMENT = "SALES_MANAGEMENT",

    /**
     * Price Desk Operations: Allows access to pricing desk features
     */
    PRICE_DESK_OPERATIONS = "PRICE_DESK_OPERATIONS",

    /**
     * Service Desk Operations: Allows access to service desk features
     */
    SERVICE_DESK_OPERATIONS = "SERVICE_DESK_OPERATIONS",

    /**
     * Regional Leadership: Allows access to regional management features
     */
    REGIONAL_LEADERSHIP = "REGIONAL_LEADERSHIP",

    /**
     * RFP Bid Management: Allows access to RFP and bid desk features
     */
    RFP_BID_MANAGEMENT = "RFP_BID_MANAGEMENT",

    /**
     * User Role Management: Allows management of user roles and permissions
     */
    USER_ROLE_MANAGEMENT = "USER_ROLE_MANAGEMENT",
}
export interface Employee {
    id: string;
    displayName: string;
    mail: string;
    mailNickname: string;
    jobTitle: string;
    mobilePhone: string;
    department: string;
    employeeId?: string;
    office?: string;
    manager?:Employee
    approverMicrosoftAccountId?: string;
  }

// Define interface for worksheet product items
export interface WorksheetProduct {
  id?: string;
  category?: 'hardware' | 'software';
  _expanded?: boolean;
  productName?: { value: string | number };
  itemNumber?: { value: string | number };
  requestSellingPrice?: { value: number };
  msrp?: { value: number };
  percentOfMsrp?: { value: string | number };
  dsdQuantity?: { value: number };
  dealerITsQuantity?: { value: number };
  subProducts?: WorksheetProduct[];
}

/**
 * Defines user roles in the application
 */
export enum UserRole {
  STANDARD = "STANDARD",
  ADMIN = "ADMIN",
  SALES_REP = "SALES_REP",
  SALES_MANAGER = "SALES_MANAGER",
  REGIONAL_LEAD = "REGIONAL_LEAD",
  PRICE_DESK_ANALYST = "PRICE_DESK_ANALYST",
  SERVICE_DESK_ANALYST = "SERVICE_DESK_ANALYST",
  DSA = "DSA",
  RFP_BID_DESK = "RFP_BID_DESK"
}

/**
 * Role interface for role management
 */
export interface Role {
  id: string;
  value: UserRole;
  label: string;
  description: string;
  permissions?: string[];
  users?: number;
}
